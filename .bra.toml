# default configuration created by the `mage watch` command.
# this file can be edited and should be checked into source control.
# see https://github.com/unknwon/bra/blob/master/templates/default.bra.toml for more configuration options.
[run]
init_cmds = [
  ["mage", "-v", "build:debug"],
  ["mage", "-v", "reloadPlugin"]
]
watch_all = true
follow_symlinks = false
ignore = [".git", "node_modules", "dist"]
ignore_files = ["mage_output_file.go"]
watch_dirs = [
  "pkg",
  "src",
]
watch_exts = [".go", ".json"]
build_delay = 2000
cmds = [
  ["mage", "-v", "build:debug"],
  ["mage", "-v", "reloadPlugin"]
]