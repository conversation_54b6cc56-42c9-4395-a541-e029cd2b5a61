{"name": "grafana-iot-sitewise-datasource", "version": "2.3.2", "description": "View IoT Sitewise data in grafana", "scripts": {"build": "webpack -c ./.config/webpack/webpack.config.ts --env production", "dev": "webpack -w -c ./.config/webpack/webpack.config.ts --env development", "e2e": "playwright test", "e2e:debug": "npx playwright test --ui", "generate-release-notes": "PREV_TAG=$(git tag | tail -n 1) && gh api --method POST /repos/grafana/iot-sitewise-datasource/releases/generate-notes -f tag_name=v${npm_package_version} -f target_commitish=main -f previous_tag_name=${PREV_TAG} | jq -r .body", "lint": "eslint --cache .", "lint:fix": "yarn run lint --fix && prettier --write --list-different .", "server": "docker compose up --build", "server:dev": "DEVELOPMENT=true yarn server", "sign": "npx --yes @grafana/sign-plugin@latest", "spellcheck": "cspell -c cspell.config.json \"**/*.{ts,tsx,js,go,md,mdx,yml,yaml,json,scss,css}\"", "test": "jest --watch --only<PERSON><PERSON>ed", "test:ci": "jest --passWithNoTests --maxWorkers 4", "test:coverage": "jest --coverage", "test:coverage:changes": "jest --coverage --changedSince=origin/main", "typecheck": "tsc --noEmit"}, "repository": "github:grafana/iot-sitewise-datasource", "author": "Grafana Labs <<EMAIL>> (https://grafana.com)", "license": "Apache-2.0", "dependencies": {"@emotion/css": "11.13.5", "@grafana/data": "^12.1.0", "@grafana/plugin-ui": "^0.10.5", "@grafana/runtime": "^12.1.0", "@grafana/schema": "^12.1.0", "@grafana/ui": "^12.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-router-dom": "^6.22.0", "tslib": "2.8.1"}, "devDependencies": {"@babel/core": "^7.28.0", "@grafana/aws-sdk": "0.7.0", "@grafana/eslint-config": "^8.0.0", "@grafana/plugin-e2e": "^2.1.7", "@grafana/tsconfig": "^2.0.0", "@playwright/test": "1.54.1", "@stylistic/eslint-plugin-ts": "^4.2.0", "@swc/core": "^1.13.2", "@swc/helpers": "^0.5.17", "@swc/jest": "^0.2.38", "@testing-library/dom": "^10.3.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "^14.6.1", "@types/glob": "^8.1.0", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.16", "@types/node": "^22.15.17", "@types/semver": "^7.7.0", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "copy-webpack-plugin": "^13.0.0", "cspell": "^9.0.1", "css-loader": "^7.1.2", "dotenv": "^17.2.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-deprecation": "^3.0.0", "eslint-plugin-jsdoc": "^51.4.1", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-webpack-plugin": "^5.0.2", "fork-ts-checker-webpack-plugin": "^9.1.0", "glob": "^11.0.2", "identity-obj-proxy": "3.0.0", "imports-loader": "^5.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.5", "lefthook": "^1.12.2", "lodash": "^4.17.21", "prettier": "^3.6.2", "replace-in-file-webpack-plugin": "^1.0.6", "sass": "1.89.2", "sass-loader": "16.0.5", "semver": "^7.7.1", "style-loader": "4.0.0", "swc-loader": "^0.2.6", "terser-webpack-plugin": "^5.3.14", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.8.3", "webpack": "^5.100.2", "webpack-cli": "^6.0.1", "webpack-livereload-plugin": "^3.0.2", "webpack-subresource-integrity": "^5.1.0", "webpack-virtual-modules": "^0.6.2", "@types/testing-library__jest-dom": "5.14.8"}, "packageManager": "yarn@1.22.19"}