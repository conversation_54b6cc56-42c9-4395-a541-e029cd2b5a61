// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	iotsitewise "github.com/aws/aws-sdk-go-v2/service/iotsitewise"
	mock "github.com/stretchr/testify/mock"
)

// SitewiseAPIClient is an autogenerated mock type for the SitewiseAPIClient type
type SitewiseAPIClient struct {
	mock.Mock
}

// BatchGetAssetPropertyAggregates provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) BatchGetAssetPropertyAggregates(_a0 context.Context, _a1 *iotsitewise.BatchGetAssetPropertyAggregatesInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.BatchGetAssetPropertyAggregatesOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for BatchGetAssetPropertyAggregates")
	}

	var r0 *iotsitewise.BatchGetAssetPropertyAggregatesOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.BatchGetAssetPropertyAggregatesInput, ...func(*iotsitewise.Options)) (*iotsitewise.BatchGetAssetPropertyAggregatesOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.BatchGetAssetPropertyAggregatesInput, ...func(*iotsitewise.Options)) *iotsitewise.BatchGetAssetPropertyAggregatesOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.BatchGetAssetPropertyAggregatesOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.BatchGetAssetPropertyAggregatesInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BatchGetAssetPropertyAggregatesPageAggregation provides a mock function with given fields: ctx, req, maxPages, maxResults
func (_m *SitewiseAPIClient) BatchGetAssetPropertyAggregatesPageAggregation(ctx context.Context, req *iotsitewise.BatchGetAssetPropertyAggregatesInput, maxPages int, maxResults int) (*iotsitewise.BatchGetAssetPropertyAggregatesOutput, error) {
	ret := _m.Called(ctx, req, maxPages, maxResults)

	if len(ret) == 0 {
		panic("no return value specified for BatchGetAssetPropertyAggregatesPageAggregation")
	}

	var r0 *iotsitewise.BatchGetAssetPropertyAggregatesOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.BatchGetAssetPropertyAggregatesInput, int, int) (*iotsitewise.BatchGetAssetPropertyAggregatesOutput, error)); ok {
		return rf(ctx, req, maxPages, maxResults)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.BatchGetAssetPropertyAggregatesInput, int, int) *iotsitewise.BatchGetAssetPropertyAggregatesOutput); ok {
		r0 = rf(ctx, req, maxPages, maxResults)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.BatchGetAssetPropertyAggregatesOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.BatchGetAssetPropertyAggregatesInput, int, int) error); ok {
		r1 = rf(ctx, req, maxPages, maxResults)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BatchGetAssetPropertyValue provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) BatchGetAssetPropertyValue(_a0 context.Context, _a1 *iotsitewise.BatchGetAssetPropertyValueInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.BatchGetAssetPropertyValueOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for BatchGetAssetPropertyValue")
	}

	var r0 *iotsitewise.BatchGetAssetPropertyValueOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.BatchGetAssetPropertyValueInput, ...func(*iotsitewise.Options)) (*iotsitewise.BatchGetAssetPropertyValueOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.BatchGetAssetPropertyValueInput, ...func(*iotsitewise.Options)) *iotsitewise.BatchGetAssetPropertyValueOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.BatchGetAssetPropertyValueOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.BatchGetAssetPropertyValueInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BatchGetAssetPropertyValueHistory provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) BatchGetAssetPropertyValueHistory(_a0 context.Context, _a1 *iotsitewise.BatchGetAssetPropertyValueHistoryInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.BatchGetAssetPropertyValueHistoryOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for BatchGetAssetPropertyValueHistory")
	}

	var r0 *iotsitewise.BatchGetAssetPropertyValueHistoryOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.BatchGetAssetPropertyValueHistoryInput, ...func(*iotsitewise.Options)) (*iotsitewise.BatchGetAssetPropertyValueHistoryOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.BatchGetAssetPropertyValueHistoryInput, ...func(*iotsitewise.Options)) *iotsitewise.BatchGetAssetPropertyValueHistoryOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.BatchGetAssetPropertyValueHistoryOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.BatchGetAssetPropertyValueHistoryInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BatchGetAssetPropertyValueHistoryPageAggregation provides a mock function with given fields: ctx, req, maxPages, maxResults
func (_m *SitewiseAPIClient) BatchGetAssetPropertyValueHistoryPageAggregation(ctx context.Context, req *iotsitewise.BatchGetAssetPropertyValueHistoryInput, maxPages int, maxResults int) (*iotsitewise.BatchGetAssetPropertyValueHistoryOutput, error) {
	ret := _m.Called(ctx, req, maxPages, maxResults)

	if len(ret) == 0 {
		panic("no return value specified for BatchGetAssetPropertyValueHistoryPageAggregation")
	}

	var r0 *iotsitewise.BatchGetAssetPropertyValueHistoryOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.BatchGetAssetPropertyValueHistoryInput, int, int) (*iotsitewise.BatchGetAssetPropertyValueHistoryOutput, error)); ok {
		return rf(ctx, req, maxPages, maxResults)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.BatchGetAssetPropertyValueHistoryInput, int, int) *iotsitewise.BatchGetAssetPropertyValueHistoryOutput); ok {
		r0 = rf(ctx, req, maxPages, maxResults)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.BatchGetAssetPropertyValueHistoryOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.BatchGetAssetPropertyValueHistoryInput, int, int) error); ok {
		r1 = rf(ctx, req, maxPages, maxResults)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DescribeAsset provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) DescribeAsset(_a0 context.Context, _a1 *iotsitewise.DescribeAssetInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.DescribeAssetOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DescribeAsset")
	}

	var r0 *iotsitewise.DescribeAssetOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.DescribeAssetInput, ...func(*iotsitewise.Options)) (*iotsitewise.DescribeAssetOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.DescribeAssetInput, ...func(*iotsitewise.Options)) *iotsitewise.DescribeAssetOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.DescribeAssetOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.DescribeAssetInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DescribeAssetModel provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) DescribeAssetModel(_a0 context.Context, _a1 *iotsitewise.DescribeAssetModelInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.DescribeAssetModelOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DescribeAssetModel")
	}

	var r0 *iotsitewise.DescribeAssetModelOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.DescribeAssetModelInput, ...func(*iotsitewise.Options)) (*iotsitewise.DescribeAssetModelOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.DescribeAssetModelInput, ...func(*iotsitewise.Options)) *iotsitewise.DescribeAssetModelOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.DescribeAssetModelOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.DescribeAssetModelInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DescribeAssetProperty provides a mock function with given fields: ctx, params, optFns
func (_m *SitewiseAPIClient) DescribeAssetProperty(ctx context.Context, params *iotsitewise.DescribeAssetPropertyInput, optFns ...func(*iotsitewise.Options)) (*iotsitewise.DescribeAssetPropertyOutput, error) {
	_va := make([]interface{}, len(optFns))
	for _i := range optFns {
		_va[_i] = optFns[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DescribeAssetProperty")
	}

	var r0 *iotsitewise.DescribeAssetPropertyOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.DescribeAssetPropertyInput, ...func(*iotsitewise.Options)) (*iotsitewise.DescribeAssetPropertyOutput, error)); ok {
		return rf(ctx, params, optFns...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.DescribeAssetPropertyInput, ...func(*iotsitewise.Options)) *iotsitewise.DescribeAssetPropertyOutput); ok {
		r0 = rf(ctx, params, optFns...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.DescribeAssetPropertyOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.DescribeAssetPropertyInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(ctx, params, optFns...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DescribeTimeSeries provides a mock function with given fields: ctx, params, optFns
func (_m *SitewiseAPIClient) DescribeTimeSeries(ctx context.Context, params *iotsitewise.DescribeTimeSeriesInput, optFns ...func(*iotsitewise.Options)) (*iotsitewise.DescribeTimeSeriesOutput, error) {
	_va := make([]interface{}, len(optFns))
	for _i := range optFns {
		_va[_i] = optFns[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DescribeTimeSeries")
	}

	var r0 *iotsitewise.DescribeTimeSeriesOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.DescribeTimeSeriesInput, ...func(*iotsitewise.Options)) (*iotsitewise.DescribeTimeSeriesOutput, error)); ok {
		return rf(ctx, params, optFns...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.DescribeTimeSeriesInput, ...func(*iotsitewise.Options)) *iotsitewise.DescribeTimeSeriesOutput); ok {
		r0 = rf(ctx, params, optFns...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.DescribeTimeSeriesOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.DescribeTimeSeriesInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(ctx, params, optFns...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ExecuteQuery provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) ExecuteQuery(_a0 context.Context, _a1 *iotsitewise.ExecuteQueryInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.ExecuteQueryOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for ExecuteQuery")
	}

	var r0 *iotsitewise.ExecuteQueryOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.ExecuteQueryInput, ...func(*iotsitewise.Options)) (*iotsitewise.ExecuteQueryOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.ExecuteQueryInput, ...func(*iotsitewise.Options)) *iotsitewise.ExecuteQueryOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.ExecuteQueryOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.ExecuteQueryInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAssetPropertyAggregates provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) GetAssetPropertyAggregates(_a0 context.Context, _a1 *iotsitewise.GetAssetPropertyAggregatesInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.GetAssetPropertyAggregatesOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAssetPropertyAggregates")
	}

	var r0 *iotsitewise.GetAssetPropertyAggregatesOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetAssetPropertyAggregatesInput, ...func(*iotsitewise.Options)) (*iotsitewise.GetAssetPropertyAggregatesOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetAssetPropertyAggregatesInput, ...func(*iotsitewise.Options)) *iotsitewise.GetAssetPropertyAggregatesOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.GetAssetPropertyAggregatesOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.GetAssetPropertyAggregatesInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAssetPropertyAggregatesPageAggregation provides a mock function with given fields: ctx, req, maxPages, maxResults
func (_m *SitewiseAPIClient) GetAssetPropertyAggregatesPageAggregation(ctx context.Context, req *iotsitewise.GetAssetPropertyAggregatesInput, maxPages int, maxResults int) (*iotsitewise.GetAssetPropertyAggregatesOutput, error) {
	ret := _m.Called(ctx, req, maxPages, maxResults)

	if len(ret) == 0 {
		panic("no return value specified for GetAssetPropertyAggregatesPageAggregation")
	}

	var r0 *iotsitewise.GetAssetPropertyAggregatesOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetAssetPropertyAggregatesInput, int, int) (*iotsitewise.GetAssetPropertyAggregatesOutput, error)); ok {
		return rf(ctx, req, maxPages, maxResults)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetAssetPropertyAggregatesInput, int, int) *iotsitewise.GetAssetPropertyAggregatesOutput); ok {
		r0 = rf(ctx, req, maxPages, maxResults)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.GetAssetPropertyAggregatesOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.GetAssetPropertyAggregatesInput, int, int) error); ok {
		r1 = rf(ctx, req, maxPages, maxResults)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAssetPropertyValue provides a mock function with given fields: ctx, params, optFns
func (_m *SitewiseAPIClient) GetAssetPropertyValue(ctx context.Context, params *iotsitewise.GetAssetPropertyValueInput, optFns ...func(*iotsitewise.Options)) (*iotsitewise.GetAssetPropertyValueOutput, error) {
	_va := make([]interface{}, len(optFns))
	for _i := range optFns {
		_va[_i] = optFns[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAssetPropertyValue")
	}

	var r0 *iotsitewise.GetAssetPropertyValueOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetAssetPropertyValueInput, ...func(*iotsitewise.Options)) (*iotsitewise.GetAssetPropertyValueOutput, error)); ok {
		return rf(ctx, params, optFns...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetAssetPropertyValueInput, ...func(*iotsitewise.Options)) *iotsitewise.GetAssetPropertyValueOutput); ok {
		r0 = rf(ctx, params, optFns...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.GetAssetPropertyValueOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.GetAssetPropertyValueInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(ctx, params, optFns...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAssetPropertyValueHistory provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) GetAssetPropertyValueHistory(_a0 context.Context, _a1 *iotsitewise.GetAssetPropertyValueHistoryInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.GetAssetPropertyValueHistoryOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAssetPropertyValueHistory")
	}

	var r0 *iotsitewise.GetAssetPropertyValueHistoryOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetAssetPropertyValueHistoryInput, ...func(*iotsitewise.Options)) (*iotsitewise.GetAssetPropertyValueHistoryOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetAssetPropertyValueHistoryInput, ...func(*iotsitewise.Options)) *iotsitewise.GetAssetPropertyValueHistoryOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.GetAssetPropertyValueHistoryOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.GetAssetPropertyValueHistoryInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAssetPropertyValueHistoryPageAggregation provides a mock function with given fields: ctx, req, maxPages, maxResults
func (_m *SitewiseAPIClient) GetAssetPropertyValueHistoryPageAggregation(ctx context.Context, req *iotsitewise.GetAssetPropertyValueHistoryInput, maxPages int, maxResults int) (*iotsitewise.GetAssetPropertyValueHistoryOutput, error) {
	ret := _m.Called(ctx, req, maxPages, maxResults)

	if len(ret) == 0 {
		panic("no return value specified for GetAssetPropertyValueHistoryPageAggregation")
	}

	var r0 *iotsitewise.GetAssetPropertyValueHistoryOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetAssetPropertyValueHistoryInput, int, int) (*iotsitewise.GetAssetPropertyValueHistoryOutput, error)); ok {
		return rf(ctx, req, maxPages, maxResults)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetAssetPropertyValueHistoryInput, int, int) *iotsitewise.GetAssetPropertyValueHistoryOutput); ok {
		r0 = rf(ctx, req, maxPages, maxResults)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.GetAssetPropertyValueHistoryOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.GetAssetPropertyValueHistoryInput, int, int) error); ok {
		r1 = rf(ctx, req, maxPages, maxResults)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetInterpolatedAssetPropertyValues provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) GetInterpolatedAssetPropertyValues(_a0 context.Context, _a1 *iotsitewise.GetInterpolatedAssetPropertyValuesInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.GetInterpolatedAssetPropertyValuesOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetInterpolatedAssetPropertyValues")
	}

	var r0 *iotsitewise.GetInterpolatedAssetPropertyValuesOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetInterpolatedAssetPropertyValuesInput, ...func(*iotsitewise.Options)) (*iotsitewise.GetInterpolatedAssetPropertyValuesOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetInterpolatedAssetPropertyValuesInput, ...func(*iotsitewise.Options)) *iotsitewise.GetInterpolatedAssetPropertyValuesOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.GetInterpolatedAssetPropertyValuesOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.GetInterpolatedAssetPropertyValuesInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetInterpolatedAssetPropertyValuesPageAggregation provides a mock function with given fields: ctx, req, maxPages, maxResults
func (_m *SitewiseAPIClient) GetInterpolatedAssetPropertyValuesPageAggregation(ctx context.Context, req *iotsitewise.GetInterpolatedAssetPropertyValuesInput, maxPages int, maxResults int) (*iotsitewise.GetInterpolatedAssetPropertyValuesOutput, error) {
	ret := _m.Called(ctx, req, maxPages, maxResults)

	if len(ret) == 0 {
		panic("no return value specified for GetInterpolatedAssetPropertyValuesPageAggregation")
	}

	var r0 *iotsitewise.GetInterpolatedAssetPropertyValuesOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetInterpolatedAssetPropertyValuesInput, int, int) (*iotsitewise.GetInterpolatedAssetPropertyValuesOutput, error)); ok {
		return rf(ctx, req, maxPages, maxResults)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.GetInterpolatedAssetPropertyValuesInput, int, int) *iotsitewise.GetInterpolatedAssetPropertyValuesOutput); ok {
		r0 = rf(ctx, req, maxPages, maxResults)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.GetInterpolatedAssetPropertyValuesOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.GetInterpolatedAssetPropertyValuesInput, int, int) error); ok {
		r1 = rf(ctx, req, maxPages, maxResults)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListAssetModels provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) ListAssetModels(_a0 context.Context, _a1 *iotsitewise.ListAssetModelsInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.ListAssetModelsOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for ListAssetModels")
	}

	var r0 *iotsitewise.ListAssetModelsOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.ListAssetModelsInput, ...func(*iotsitewise.Options)) (*iotsitewise.ListAssetModelsOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.ListAssetModelsInput, ...func(*iotsitewise.Options)) *iotsitewise.ListAssetModelsOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.ListAssetModelsOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.ListAssetModelsInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListAssetProperties provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) ListAssetProperties(_a0 context.Context, _a1 *iotsitewise.ListAssetPropertiesInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.ListAssetPropertiesOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for ListAssetProperties")
	}

	var r0 *iotsitewise.ListAssetPropertiesOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.ListAssetPropertiesInput, ...func(*iotsitewise.Options)) (*iotsitewise.ListAssetPropertiesOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.ListAssetPropertiesInput, ...func(*iotsitewise.Options)) *iotsitewise.ListAssetPropertiesOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.ListAssetPropertiesOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.ListAssetPropertiesInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListAssets provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) ListAssets(_a0 context.Context, _a1 *iotsitewise.ListAssetsInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.ListAssetsOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for ListAssets")
	}

	var r0 *iotsitewise.ListAssetsOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.ListAssetsInput, ...func(*iotsitewise.Options)) (*iotsitewise.ListAssetsOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.ListAssetsInput, ...func(*iotsitewise.Options)) *iotsitewise.ListAssetsOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.ListAssetsOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.ListAssetsInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListAssociatedAssets provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) ListAssociatedAssets(_a0 context.Context, _a1 *iotsitewise.ListAssociatedAssetsInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.ListAssociatedAssetsOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for ListAssociatedAssets")
	}

	var r0 *iotsitewise.ListAssociatedAssetsOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.ListAssociatedAssetsInput, ...func(*iotsitewise.Options)) (*iotsitewise.ListAssociatedAssetsOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.ListAssociatedAssetsInput, ...func(*iotsitewise.Options)) *iotsitewise.ListAssociatedAssetsOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.ListAssociatedAssetsOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.ListAssociatedAssetsInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListTimeSeries provides a mock function with given fields: _a0, _a1, _a2
func (_m *SitewiseAPIClient) ListTimeSeries(_a0 context.Context, _a1 *iotsitewise.ListTimeSeriesInput, _a2 ...func(*iotsitewise.Options)) (*iotsitewise.ListTimeSeriesOutput, error) {
	_va := make([]interface{}, len(_a2))
	for _i := range _a2 {
		_va[_i] = _a2[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _a0, _a1)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for ListTimeSeries")
	}

	var r0 *iotsitewise.ListTimeSeriesOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.ListTimeSeriesInput, ...func(*iotsitewise.Options)) (*iotsitewise.ListTimeSeriesOutput, error)); ok {
		return rf(_a0, _a1, _a2...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *iotsitewise.ListTimeSeriesInput, ...func(*iotsitewise.Options)) *iotsitewise.ListTimeSeriesOutput); ok {
		r0 = rf(_a0, _a1, _a2...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iotsitewise.ListTimeSeriesOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *iotsitewise.ListTimeSeriesInput, ...func(*iotsitewise.Options)) error); ok {
		r1 = rf(_a0, _a1, _a2...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewSitewiseAPIClient creates a new instance of SitewiseAPIClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSitewiseAPIClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *SitewiseAPIClient {
	mock := &SitewiseAPIClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
