{"AssetModelArn": "arn:aws:iotsitewise:us-east-1:526544423884:asset-model/1f95cf92-34ff-4975-91a9-e9f2af35b6a5", "AssetModelCompositeModels": [], "AssetModelCreationDate": "2020-11-24T02:17:49Z", "AssetModelDescription": "This is an asset model used in the IoT SiteWise Demo for representing a turbine in a wind farm. It will be deleted at the end of the demo.", "AssetModelHierarchies": [], "AssetModelId": "1f95cf92-34ff-4975-91a9-e9f2af35b6a5", "AssetModelLastUpdateDate": "2020-11-24T02:17:54Z", "AssetModelName": "<PERSON><PERSON>", "AssetModelProperties": [{"DataType": "STRING", "DataTypeSpec": null, "Id": "3a245608-2efb-4a77-8260-ff3d179e1304", "Name": "Make", "Type": {"Attribute": {"DefaultValue": "Amazon"}, "Measurement": null, "Metric": null, "Transform": null}, "Unit": null}, {"DataType": "INTEGER", "DataTypeSpec": null, "Id": "f7c35bca-2371-49a2-9953-51ef48f080a0", "Name": "Model", "Type": {"Attribute": {"DefaultValue": "500"}, "Measurement": null, "Metric": null, "Transform": null}, "Unit": null}, {"DataType": "STRING", "DataTypeSpec": null, "Id": "9bd6e3c7-ea4a-4938-a46c-77b8ee3e3414", "Name": "Location", "Type": {"Attribute": {"DefaultValue": "Ren<PERSON>"}, "Measurement": null, "Metric": null, "Transform": null}, "Unit": null}, {"DataType": "DOUBLE", "DataTypeSpec": null, "Id": "93efe943-34a7-416d-8e38-c23dd86b5a7d", "Name": "<PERSON><PERSON> (KiloNewton Meter)", "Type": {"Attribute": null, "Measurement": {}, "Metric": null, "Transform": null}, "Unit": "kNm"}, {"DataType": "DOUBLE", "DataTypeSpec": null, "Id": "14b18bf4-7eaa-4810-9b28-b17ad8d68392", "Name": "Wind Direction", "Type": {"Attribute": null, "Measurement": {}, "Metric": null, "Transform": null}, "Unit": "Degrees"}, {"DataType": "DOUBLE", "DataTypeSpec": null, "Id": "fbe3d24f-8313-4c92-8f3d-60cdba434af2", "Name": "RotationsPerMinute", "Type": {"Attribute": null, "Measurement": {}, "Metric": null, "Transform": null}, "Unit": "RPM"}, {"DataType": "DOUBLE", "DataTypeSpec": null, "Id": "3627f45d-710a-47c8-ae6c-4b71f7c9f5eb", "Name": "Wind Speed", "Type": {"Attribute": null, "Measurement": {}, "Metric": null, "Transform": null}, "Unit": "m/s"}, {"DataType": "DOUBLE", "DataTypeSpec": null, "Id": "6debde03-4c6d-415a-8f5c-799638b5c4f8", "Name": "<PERSON><PERSON> (<PERSON>)", "Type": {"Attribute": null, "Measurement": null, "Metric": null, "Transform": {"Expression": "knm * 1000", "Variables": [{"Name": "knm", "Value": {"HierarchyId": null, "PropertyId": "93efe943-34a7-416d-8e38-c23dd86b5a7d"}}]}}, "Unit": "Nm"}, {"DataType": "DOUBLE", "DataTypeSpec": null, "Id": "ca101711-9848-420c-bef2-52bd2d06c2b2", "Name": "RotationsPerSecond", "Type": {"Attribute": null, "Measurement": null, "Metric": null, "Transform": {"Expression": "rpm / 60", "Variables": [{"Name": "rpm", "Value": {"HierarchyId": null, "PropertyId": "fbe3d24f-8313-4c92-8f3d-60cdba434af2"}}]}}, "Unit": "RPS"}, {"DataType": "DOUBLE", "DataTypeSpec": null, "Id": "938d129d-6119-46d9-ab05-a93c69717bb7", "Name": "Overdrive State", "Type": {"Attribute": null, "Measurement": null, "Metric": null, "Transform": {"Expression": "gte(torque,3)", "Variables": [{"Name": "torque", "Value": {"HierarchyId": null, "PropertyId": "93efe943-34a7-416d-8e38-c23dd86b5a7d"}}]}}, "Unit": null}, {"DataType": "DOUBLE", "DataTypeSpec": null, "Id": "e6c52ea3-d746-46df-b843-d0459540d584", "Name": "Average Wind Speed", "Type": {"Attribute": null, "Measurement": null, "Metric": {"Window": {"Tumbling": {"Interval": "5m"}}, "Expression": "avg(windspeed)", "Variables": [{"Name": "windspeed", "Value": {"HierarchyId": null, "PropertyId": "3627f45d-710a-47c8-ae6c-4b71f7c9f5eb"}}]}, "Transform": null}, "Unit": "m/s"}, {"DataType": "DOUBLE", "DataTypeSpec": null, "Id": "86294f4c-d5fe-4973-9acd-4fbfd8fb32bc", "Name": "Overdrive State Time", "Type": {"Attribute": null, "Measurement": null, "Metric": {"Window": {"Tumbling": {"Interval": "5m"}}, "Expression": "statetime(overdrive_state)", "Variables": [{"Name": "overdrive_state", "Value": {"HierarchyId": null, "PropertyId": "938d129d-6119-46d9-ab05-a93c69717bb7"}}]}, "Transform": null}, "Unit": "Seconds"}, {"DataType": "DOUBLE", "DataTypeSpec": null, "Id": "a1e3fc31-b7d3-4d96-9612-b1eb080fce6b", "Name": "Average Power", "Type": {"Attribute": null, "Measurement": null, "Metric": {"Expression": "avg(torque) * avg(rps) * 2 * 3.14", "Window": {"Tumbling": {"Interval": "5m"}}, "Variables": [{"Name": "rps", "Value": {"HierarchyId": null, "PropertyId": "ca101711-9848-420c-bef2-52bd2d06c2b2"}}, {"Name": "torque", "Value": {"HierarchyId": null, "PropertyId": "6debde03-4c6d-415a-8f5c-799638b5c4f8"}}]}, "Transform": null}, "Unit": "<PERSON>"}], "AssetModelStatus": {"Error": null, "State": "ACTIVE"}}