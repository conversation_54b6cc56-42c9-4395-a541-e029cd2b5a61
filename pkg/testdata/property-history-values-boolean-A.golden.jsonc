//  🌟 This was machine generated.  Do not edit. 🌟
//  
//  Frame[0] {
//      "typeVersion": [
//          0,
//          0
//      ],
//      "custom": {
//          "entryId": "f17e550c5c0b507a000c9c98fc567e31bab62a6b60578a876c3e6eeeb9c58972",
//          "resolution": "RAW"
//      }
//  }
//  Name: Demo Turbine Asset 1
//  Dimensions: 3 Fields by 35 Rows
//  +-------------------------------+----------------+----------------+
//  | Name: time                    | Name: Is Windy | Name: quality  |
//  | Labels:                       | Labels:        | Labels:        |
//  | Type: []time.Time             | Type: []bool   | Type: []string |
//  +-------------------------------+----------------+----------------+
//  | 2021-02-01 17:30:00 +0100 CET | true           | GOOD           |
//  | 2021-02-01 17:35:00 +0100 CET | true           | GOOD           |
//  | 2021-02-01 17:40:00 +0100 CET | false          | GOOD           |
//  | 2021-02-01 17:45:00 +0100 CET | false          | GOOD           |
//  | 2021-02-01 17:50:00 +0100 CET | true           | GOOD           |
//  | 2021-02-01 17:55:00 +0100 CET | true           | GOOD           |
//  | 2021-02-01 18:00:00 +0100 CET | false          | GOOD           |
//  | 2021-02-01 18:05:00 +0100 CET | false          | GOOD           |
//  | 2021-02-01 18:10:00 +0100 CET | true           | GOOD           |
//  | ...                           | ...            | ...            |
//  +-------------------------------+----------------+----------------+
//  
//  
//  🌟 This was machine generated.  Do not edit. 🌟
{
  "status": 200,
  "frames": [
    {
      "schema": {
        "name": "Demo Turbine Asset 1",
        "meta": {
          "typeVersion": [
            0,
            0
          ],
          "custom": {
            "entryId": "f17e550c5c0b507a000c9c98fc567e31bab62a6b60578a876c3e6eeeb9c58972",
            "resolution": "RAW"
          }
        },
        "fields": [
          {
            "name": "time",
            "type": "time",
            "typeInfo": {
              "frame": "time.Time"
            }
          },
          {
            "name": "Is Windy",
            "type": "boolean",
            "typeInfo": {
              "frame": "bool"
            },
            "config": {
              "unit": "none"
            }
          },
          {
            "name": "quality",
            "type": "string",
            "typeInfo": {
              "frame": "string"
            }
          }
        ]
      },
      "data": {
        "values": [
          [
            1612197000000,
            1612197300000,
            1612197600000,
            1612197900000,
            1612198200000,
            1612198500000,
            1612198800000,
            1612199100000,
            1612199400000,
            1612199700000,
            1612200000000,
            1612200300000,
            1612200600000,
            1612200900000,
            1612201200000,
            1612201500000,
            1612201800000,
            1612202100000,
            1612202400000,
            1612202700000,
            1612203000000,
            1612203300000,
            1612203600000,
            1612203900000,
            1612204200000,
            1612204500000,
            1612204800000,
            1612205100000,
            1612205400000,
            1612205700000,
            1612206000000,
            1612206300000,
            1612206600000,
            1612206900000,
            1612207200000
          ],
          [
            true,
            true,
            false,
            false,
            true,
            true,
            false,
            false,
            true,
            true,
            false,
            false,
            true,
            true,
            false,
            false,
            true,
            true,
            false,
            false,
            true,
            true,
            false,
            false,
            true,
            true,
            false,
            false,
            true,
            true,
            false,
            false,
            true,
            true,
            false
          ],
          [
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD",
            "GOOD"
          ]
        ]
      }
    }
  ]
}