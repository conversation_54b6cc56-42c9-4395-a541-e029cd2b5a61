<!--

Thank you for sending a pull request! Here are some tips:

1. To surface this PR in the changelog add the label: changelog
    If this PR is going in the changelog please make sure the title of the PR explains the feature in a user-centric way:
        Bad: fix state bug in hooks
        Good: Fix crash when switching from Query Builder

2. Ensure you include and run the appropriate tests as part of your Pull Request.

3. In a new feature or configuration option, consider updating the documentation in README.md(https://github.com/grafana/iot-sitewise-datasource/blob/main/README.md) or [src/README.md](https://github.com/grafana/iot-sitewise-datasource/blob/main/README.md).

-->

**What this PR does / why we need it**:

**Which issue(s) this PR fixes**:

<!--

- Automatically closes linked issue when the Pull Request is merged.

Usage: "Fixes #<issue number>", or "Fixes (paste link of issue)"

-->

Fixes #

**Special notes for your reviewer**: