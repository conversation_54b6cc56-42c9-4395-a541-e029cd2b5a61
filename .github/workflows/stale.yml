name: 'Close stale issues'
on:
  schedule:
    # run at 1:30 every day
    - cron: '30 1 * * *'

permissions:
  issues: write

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          # start from the oldest issues when performing stale operations
          ascending: true
          days-before-issue-stale: 365
          days-before-issue-close: 30
          stale-issue-label: stale
          exempt-issue-labels: no stalebot,type/epic
          stale-issue-message: >
            This issue has been automatically marked as stale because it has not had
            activity in the last year. It will be closed in 30 days if no further activity occurs. Please
            feel free to leave a comment if you believe the issue is still relevant.
            Thank you for your contributions!
          close-issue-message: >
            This issue has been automatically closed because it has not had any further
            activity in the last 30 days. Thank you for your contributions!
