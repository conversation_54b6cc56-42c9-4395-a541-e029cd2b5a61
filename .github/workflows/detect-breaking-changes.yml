name: Compatibility check
on: [push, pull_request]
permissions: 
  contents: read
jobs:
  compatibilitycheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with: 
          persist-credentials: false
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
      - name: Install dependencies
        run: yarn install
      - name: Build plugin
        run: yarn build
      - name: Compatibility check
        uses: grafana/plugin-actions/is-compatible@v1
        with:
          module: './src/module.ts'
          comment-pr: 'no'
          fail-if-incompatible: 'yes'
          targets: '@grafana/data,@grafana/ui,@grafana/runtime'
