name: Plugins - CI

on:
  push:
    branches:
      - main
  pull_request:

permissions: {}

jobs:
  ci:
    name: CI-plugins
    uses: grafana/plugin-ci-workflows/.github/workflows/ci.yml@main # zizmor: ignore[unpinned-uses]
    permissions:
      contents: read
      id-token: write
    with:
      plugin-version-suffix: ${{ github.event_name == 'pull_request' && github.event.pull_request.head.sha || '' }}
      golangci-lint-version: '2.1.6'
      playwright-secrets: |
        AWS_ACCESS_KEY=e2e:AWS_ACCESS_KEY
        AWS_SECRET_KEY=e2e:AWS_SECRET_KEY
