name: Run commands when issues are labeled
on:
  issues:
    types: [labeled, unlabeled]
permissions: 
  contents: read
  issues: write
jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Actions
        uses: actions/checkout@v4
        with:
          repository: 'grafana/grafana-github-actions'
          path: ./actions
          ref: main
          persist-credentials: false
      - name: Install Actions
        run: npm install --production --prefix ./actions
      - name: Get secrets from vault
        id: get-secrets
        uses: grafana/shared-workflows/actions/get-vault-secrets@main
        with:
          repo_secrets: |
            AWS_DS_TOKEN_CREATOR_ID=aws-ds-token-creator:app_id
            AWS_DS_TOKEN_CREATOR_PEM=aws-ds-token-creator:pem
      - name: 'Generate token'
        id: generate_token
        uses: tibdex/github-app-token@3beb63f4bd073e61482598c45c71c1019b59b73a
        with:
          app_id: ${{ env.AWS_DS_TOKEN_CREATOR_ID }}
          private_key: ${{ env.<PERSON><PERSON>_DS_TOKEN_CREATOR_PEM }}
      - name: Run Commands
        uses: ./actions/commands
        with:
          token: ${{ steps.generate_token.outputs.token }}
          configPath: issue_commands
