name: Dependabot reviewer
on: pull_request
permissions:
  pull-requests: write
  contents: write
jobs:
  call-workflow-passing-data:
    uses: grafana/security-github-actions/.github/workflows/dependabot-automerge.yaml@main
    with:
      packages-minor-autoupdate: '["@emotion/css","@grafana/data","@grafana/plugin-ui","@grafana/runtime","@grafana/schema","@grafana/ui","tslib","github.com/aws/aws-sdk-go-v2","github.com/aws/aws-sdk-go-v2/config","github.com/aws/aws-sdk-go-v2/service/iotsitewise","github.com/aws/smithy-go","github.com/google/go-cmp","github.com/grafana/grafana-aws-sdk","github.com/grafana/grafana-plugin-sdk-go","github.com/magefile/mage","github.com/patrickmn/go-cache","github.com/pkg/errors","github.com/stretchr/testify"]'
      repository-merge-method: 'squash'
