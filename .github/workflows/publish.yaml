name: Plugins - CD
run-name: Deploy ${{ inputs.branch }} to ${{ inputs.environment }} by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      branch:
        description: Branch to publish from. Can be used to deploy PRs to dev
        default: main
      environment:
        description: Environment to publish to
        required: true
        type: choice
        options:
          - "dev"
          - "ops"
          - "prod"
      docs-only:
        description: Only publish docs, do not publish the plugin
        default: false
        type: boolean

permissions: {}

jobs:
  cd:
    name: CD-plugins
    uses: grafana/plugin-ci-workflows/.github/workflows/cd.yml@main # zizmor: ignore[unpinned-uses]
    permissions:
      contents: write
      id-token: write
      attestations: write
    with:
      branch: ${{ github.event.inputs.branch }}
      environment: ${{ github.event.inputs.environment }}
      docs-only: ${{ fromJSON(github.event.inputs.docs-only) }}
      golangci-lint-version: '2.1.6'
      playwright-secrets: |
        AWS_ACCESS_KEY=e2e:AWS_ACCESS_KEY
        AWS_SECRET_KEY=e2e:AWS_SECRET_KEY

      # Scope for the plugin published to the catalog. Setting this to "grafana_cloud" will make it visible only in Grafana Cloud
      # (and hide it for on-prem). This is required for some provisioned plugins.
      # scopes: grafana_cloud

      # Also deploy the plugin to Grafana Cloud via Argo. You also have to follow the Argo Workflows setup guide for this to work.
      # grafana-cloud-deployment-type: provisioned
      # argo-workflow-slack-channel: "#grafana-plugins-platform-ci"