pre-commit:
  parallel: true
  commands:
    frontend-lint:
      glob: '*.{js,ts,tsx}'
      run: |
        eslint --cache --ignore-path ./.gitignore --fix {staged_files}
        yarn prettier --write {staged_files}
      stage_fixed: true
    backend-format:
      glob: '*pkg/**/*.go'
      run: gofmt -w -s {staged_files}
      stage_fixed: true
    spellcheck:
      run: yarn cspell -c cspell.config.json --no-must-find-files \"**/*.{ts,tsx,js,go,md,mdx,yml,yaml,json,scss,css}\" {staged_files}
