{"version": "0.2.0", "configurations": [{"name": "Run standalone plugin", "type": "go", "request": "launch", "mode": "auto", "program": "${workspaceFolder}/pkg/", "env": {}, "args": ["--standalone=true"]}, {"name": "Attach to plugin backend in docker", "type": "go", "request": "attach", "mode": "remote", "port": 2345, "host": "127.0.0.1", "showLog": true, "trace": "log", "logOutput": "rpc", "substitutePath": [{"from": "${workspaceFolder}", "to": "/root/grafana-iot-sitewise-datasource"}]}]}