services:
  grafana:
    user: root
    container_name: 'grafana-iot-sitewise-datasource'

    build:
      context: .
      args:
        grafana_image: ${GRAFANA_IMAGE:-grafana-enterprise}
        grafana_version: ${GRAFANA_VERSION:-12.1.0}
        development: ${DEVELOPMENT:-false}
        anonymous_auth_enabled: ${ANONYMOUS_AUTH_ENABLED:-true}
    ports:
      - 3000:3000/tcp
      - 2345:2345/tcp # delve
    security_opt:
      - 'apparmor:unconfined'
      - 'seccomp:unconfined'
    cap_add:
      - SYS_PTRACE
    volumes:
      - ../dist:/var/lib/grafana/plugins/grafana-iot-sitewise-datasource
      - ../provisioning:/etc/grafana/provisioning
      - ..:/root/grafana-iot-sitewise-datasource

    environment:
      NODE_ENV: development
      GF_LOG_FILTERS: plugin.grafana-iot-sitewise-datasource:debug
      GF_LOG_LEVEL: debug
      GF_DATAPROXY_LOGGING: 1
      GF_PLUGINS_ALLOW_LOADING_UNSIGNED_PLUGINS: grafana-iot-sitewise-datasource
