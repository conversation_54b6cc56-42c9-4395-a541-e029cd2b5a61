[supervisord]
nodaemon=true
user=root

[program:grafana]
user=root
directory=/var/lib/grafana
command=bash -c 'while [ ! -f /root/grafana-iot-sitewise-datasource/dist/gpx_iot_sitewise* ]; do sleep 1; done; /run.sh'
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
redirect_stderr=true
killasgroup=true
stopasgroup=true
autostart=true

[program:delve]
user=root
command=/bin/bash -c 'pid=""; while [ -z "$pid" ]; do pid=$(pgrep -f gpx_iot_sitewise); done; /root/go/bin/dlv attach --api-version=2 --headless --continue --accept-multiclient --listen=:2345 $pid'
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
redirect_stderr=true
killasgroup=false
stopasgroup=false
autostart=true
autorestart=true

[program:build-watcher]
user=root
command=/bin/bash -c 'while inotifywait -e modify,create,delete -r /var/lib/grafana/plugins/grafana-iot-sitewise-datasource; do echo "Change detected, restarting delve...";supervisorctl restart delve; done'
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
redirect_stderr=true
killasgroup=true
stopasgroup=true
autostart=true

[program:mage-watcher]
user=root
environment=PATH="/usr/local/go/bin:/root/go/bin:%(ENV_PATH)s"
directory=/root/grafana-iot-sitewise-datasource
command=/bin/bash -c 'git config --global --add safe.directory /root/grafana-iot-sitewise-datasource && mage -v watch'
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
redirect_stderr=true
killasgroup=true
stopasgroup=true
autostart=true
