# Compatibility

AWS IoT Sitewise data source plugin >=2.0.0 is not compatible with Grafana versions <=10.4.x due to a breaking change in UI components.

# AWS IoT Sitewise Data Source Development Guide

Please add feedback to the [issues](https://github.com/grafana/iot-sitewise-datasource/issues) folder, and we will follow up shortly. Be sure to include version information for both grafana and the installed plugin.

The production plugins can be downloaded from [the IoT sitewise plugin page](https://grafana.com/grafana/plugins/grafana-iot-sitewise-datasource/installation).

For configuration options, see: [src/README.md](src/README.md)

## Development builds

To get the latest build artifacts for a branch:

1. find the green checkbox after a build
2. click link to the "package" details link
3. open the "Artifacts" tab
4. Pick the appropriate download for your platform

<table>
  <tr>
    <td><img src="https://raw.githubusercontent.com/grafana/iot-sitewise-datasource/main/docs/package.png" /></td>
    <td><img src="https://raw.githubusercontent.com/grafana/iot-sitewise-datasource/main/docs/artifacts.png" /></td>
  </tr>
</table>
