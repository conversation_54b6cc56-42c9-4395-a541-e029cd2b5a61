import React, { Component } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Tab, TabC<PERSON>nt, TabsBar } from '@grafana/ui';
import { AssetInfo } from '../../types';
import { DataSource } from 'SitewiseDataSource';
import { SitewiseCache } from 'sitewiseCache';
import { BrowseModels } from './BrowseModels';
import { BrowseHierarchy } from './BrowseHierarchy';
import { type Region } from '../../regions';

export interface Props {
  datasource: DataSource;
  assetId?: string; // The incoming value
  region?: Region;
  onAssetChanged: (assetId?: string) => void;
}

interface State {
  isOpen: boolean;
  tab: 'Modal' | 'Hierarchy';
  cache?: SitewiseCache;
  asset?: AssetInfo;
}

export const ModalHeader = () => {
  return (
    <div className="modal-header-title">
      <Icon name="folder-open" size="lg" />
      <span className="p-l-1">Asset Browser</span>
    </div>
  );
};

export class AssetBrowser extends Component<Props, State> {
  state: State = { isOpen: false, tab: 'Hierarchy' };

  async componentDidMount() {
    const { assetId, region } = this.props;
    const cache = this.props.datasource.getCache(region);
    const asset = assetId ? await cache.getAssetInfo(assetId) : undefined;
    this.setState({ cache, asset });
  }

  async componentDidUpdate(oldProps: Props) {
    const update: State = { ...this.state };
    let shouldUpdate = false;

    if (this.props.region !== oldProps.region) {
      shouldUpdate = true;
      update.cache = this.props.datasource.getCache(this.props.region);
    }

    if (this.props.assetId !== oldProps.assetId) {
      const { cache } = this.state;
      const { assetId } = this.props;
      shouldUpdate = true;
      // Asset changed from the parent... reset state
      update.asset = assetId ? await cache!.getAssetInfo(assetId) : undefined;
    }

    if (shouldUpdate) {
      this.setState(update);
    }
  }

  onSelectAsset = (assetId?: string) => {
    this.props.onAssetChanged(assetId);
    this.setState({ isOpen: false });
  };

  renderBody() {
    const { cache, tab, asset } = this.state;
    if (!cache) {
      return (
        <div>
          <Spinner />
          Loading...
        </div>
      );
    }

    switch (tab) {
      case 'Hierarchy':
        return <BrowseHierarchy cache={cache} asset={asset} onAssetSelected={this.onSelectAsset} />;
      case 'Modal':
        return <BrowseModels cache={cache} asset={asset} onAssetChanged={this.onSelectAsset} />;
    }
  }

  render() {
    const { isOpen, tab } = this.state;

    return (
      <>
        <Button
          variant="secondary"
          size="md"
          icon="folder-open"
          onClick={(event) =>
            this.setState({ isOpen: true }, () => {
              console.log(this.state);
            })
          }
        >
          Explore
        </Button>
        <Modal title={<ModalHeader />} isOpen={isOpen} onDismiss={() => this.setState({ isOpen: false })}>
          <div>
            <div>
              <TabsBar>
                <Tab
                  label={'Hierarchy'}
                  active={'Hierarchy' === tab}
                  onChangeTab={() => this.setState({ tab: 'Hierarchy' })}
                />
                <Tab label={'By Model'} active={'Modal' === tab} onChangeTab={() => this.setState({ tab: 'Modal' })} />
              </TabsBar>
              <TabContent style={{ maxHeight: '90vh' }}>
                <div>{this.renderBody()}</div>
              </TabContent>
            </div>
          </div>
        </Modal>
      </>
    );
  }
}
