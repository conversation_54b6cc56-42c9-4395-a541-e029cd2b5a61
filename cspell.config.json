{"language": "en", "ignorePaths": ["coverage/**", "dist/**", ".config/**", "go.mod", "go.sum", "mage_output_file.go", "node_modules/**", "pkg/**/*_test.go", "provisioning/**/*.yaml", "**/testdata/*.json", "**/testdata/*.jsonc", "**/dashboards/*.json", "src/static/**", "vendor/**", "yarn.lock"], "words": ["Sitewise", "sitewise", "datasource", "datasources", "isob", "testid", "testdata", "vbytes", "Vals", "<PERSON><PERSON>", "Collapsable", "pat<PERSON><PERSON>n", "awsds", "swclient", "swcfg", "sess", "sesh", "iotsitewiseiface", "awsds", "stretchr", "propvals", "LOCF", "srvr", "instancemgmt", "nanos", "stddev", "kwatt", "magefile", "dserrors", "querytype", "timeseries", "nolint", "staticcheck", "typecheck", "commitish", "dontHide", "renton", "assetids", "ahom", "opentelemetry", "httptrace", "otelhttptrace", "idastambuk", "fridgepoet", "errgroup", "ectx", "reqs", "struct", "combobox", "Cacheable", "Stalebot", "gofmt", "eslintcache", "lefthook", "ssjagad", "jackspeak", "nvmrc", "gomod", "<PERSON><PERSON><PERSON>", "subresource", "seccomp", "PTRACE", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tibdex", "sqlutil", "stdev", "sqlds", "errorsource", "viant", "unmarshalling", "assetid", "propid", "compatibilitycheck", "ziz<PERSON>", "iotsitewisetypes", "awsconfig", "<PERSON><PERSON><PERSON><PERSON>", "httpclient", "propid", "smithyhttp", "eslintv", "cooldown", "Rspack"]}